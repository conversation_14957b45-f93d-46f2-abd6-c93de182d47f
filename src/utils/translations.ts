
import { Language } from '@/contexts/LanguageContext';

export const translations = {
  en: {
    title: "Epicycloid Demonstrator",
    subtitle: "Observe the trajectory of a circle with radius r rolling outside a circle with radius R without slipping",
    controlPanel: "Control Panel",
    start: "Start",
    stop: "Stop",
    innerRadius: "Inner Radius R",
    outerRadius: "Outer Radius r",
    markerAngle: "Marker Angle",
    targetRotations: "Target Rotations (0=unlimited)",
    speed: "Rolling Speed",
    clockwise: "Clockwise",
    counterclockwise: "Counter-clockwise",
    showTrajectory: "Show Trajectory",
    statistics: "Statistics",
    currentRotations: "Current Rotations",
    targetRotationsLabel: "Target Rotations",
    centerDistance: "Center Distance",
    markerDistance: "Marker Distance",
    language: "Language"
  },
  zh: {
    title: "圆外摆线演示器",
    subtitle: "观察半径为r的圆在半径为R的圆外无滑动滚动的轨迹",
    controlPanel: "控制面板",
    start: "开始",
    stop: "停止",
    innerRadius: "内圆半径 R",
    outerRadius: "外圆半径 r",
    markerAngle: "标记点角度",
    targetRotations: "目标滚动圈数 (0=无限制)",
    speed: "滚动速度",
    clockwise: "顺时针",
    counterclockwise: "逆时针",
    showTrajectory: "显示轨迹",
    statistics: "统计信息",
    currentRotations: "当前圈数",
    targetRotationsLabel: "目标圈数",
    centerDistance: "圆心距离",
    markerDistance: "标记点距离",
    language: "语言"
  }
};

export const t = (key: keyof typeof translations.en, language: Language): string => {
  return translations[language][key];
};
