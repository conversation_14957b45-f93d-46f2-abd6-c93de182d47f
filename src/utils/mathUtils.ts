export interface Point {
  x: number;
  y: number;
}

/**
 * 计算圆外摆线上点的位置
 * @param innerRadius 内圆半径 R
 * @param outerRadius 外圆半径 r  
 * @param angle 当前角度 θ
 * @param markerAngle 标记点在外圆上的角度
 * @returns 标记点的坐标
 */
export const calculateEpicycloidPoint = (
  innerRadius: number,
  outerRadius: number,
  angle: number,
  markerAngle: number
): Point => {
  // 圆外摆线的参数方程
  // x = (R + r) * cos(θ) - r * cos((R + r) / r * θ + φ)
  // y = (R + r) * sin(θ) - r * sin((R + r) / r * θ + φ)
  // 其中 φ 是标记点在外圆上的初始角度
  
  const R = innerRadius;
  const r = outerRadius;
  const theta = angle;
  const phi = markerAngle * Math.PI / 180; // 转换为弧度
  
  const x = (R + r) * Math.cos(theta) - r * Math.cos((R + r) / r * theta + phi);
  const y = (R + r) * Math.sin(theta) - r * Math.sin((R + r) / r * theta + phi);
  
  return { x, y };
};

/**
 * 计算外圆中心的位置
 * @param innerRadius 内圆半径
 * @param outerRadius 外圆半径
 * @param angle 当前角度
 * @returns 外圆中心的坐标
 */
export const calculateOuterCircleCenter = (
  innerRadius: number,
  outerRadius: number,
  angle: number
): Point => {
  const distance = innerRadius + outerRadius;
  const x = distance * Math.cos(angle);
  const y = distance * Math.sin(angle);
  
  return { x, y };
};

/**
 * 计算两点之间的距离
 * @param p1 第一个点
 * @param p2 第二个点
 * @returns 距离
 */
export const calculateDistance = (p1: Point, p2: Point): number => {
  return Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2);
};

/**
 * 将角度从弧度转换为度
 * @param radians 弧度值
 * @returns 度数值
 */
export const radiansToDegrees = (radians: number): number => {
  return radians * 180 / Math.PI;
};

/**
 * 将角度从度转换为弧度
 * @param degrees 度数值
 * @returns 弧度值
 */
export const degreesToRadians = (degrees: number): number => {
  return degrees * Math.PI / 180;
};

/**
 * Calculate theoretical trajectory length for outer circle center
 * @param innerRadius Inner circle radius R
 * @param outerRadius Outer circle radius r
 * @param rotations Number of rotations
 * @returns Theoretical center trajectory length
 */
export const calculateCenterTrajectoryLength = (
  innerRadius: number,
  outerRadius: number,
  rotations: number
): number => {
  // Center moves in a circle with radius (R + r)
  const centerRadius = innerRadius + outerRadius;
  return 2 * Math.PI * centerRadius * rotations;
};

/**
 * Calculate theoretical trajectory length for marker point
 * @param innerRadius Inner circle radius R
 * @param outerRadius Outer circle radius r
 * @param rotations Number of rotations
 * @returns Theoretical marker trajectory length
 */
export const calculateMarkerTrajectoryLength = (
  innerRadius: number,
  outerRadius: number,
  rotations: number
): number => {
  // This is an approximation for epicycloid arc length
  // The exact formula is complex, but this gives a good estimation
  const k = (innerRadius + outerRadius) / outerRadius;
  const approximateLength = 2 * Math.PI * (innerRadius + outerRadius) * k * rotations;
  return approximateLength;
};
