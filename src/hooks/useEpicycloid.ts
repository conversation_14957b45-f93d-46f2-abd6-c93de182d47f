import { useState, useEffect, useRef } from "react";
import { calculateEpicycloidPoint, calculateOuterCircleCenter, Point, calculateDistance, calculateCenterTrajectoryLength, calculateMarkerTrajectoryLength } from "@/utils/mathUtils";

export const useEpicycloid = (
  innerRadius: number, 
  outerRadius: number, 
  markerAngle: number, 
  speed: number,
  clockwise: boolean = true,
  targetRotations: number = 0
) => {
  const [currentAngle, setCurrentAngle] = useState(0);
  const [trajectoryPoints, setTrajectoryPoints] = useState<Point[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [shadowCircles, setShadowCircles] = useState<Point[]>([]);
  const [actualCenterDistance, setActualCenterDistance] = useState(0);
  const [actualMarkerDistance, setActualMarkerDistance] = useState(0);
  const [lastCenterPosition, setLastCenterPosition] = useState<Point>({ x: 0, y: 0 });
  const [lastMarkerPosition, setLastMarkerPosition] = useState<Point>({ x: 0, y: 0 });
  const [completedRotations, setCompletedRotations] = useState(0);
  const animationRef = useRef<number>();

  // 计算外圆转动的角度和圈数（考虑方向）
  const directionMultiplier = clockwise ? 1 : -1;
  const outerCircleRotationAngle = outerRadius > 0 ? Math.abs(currentAngle) * (innerRadius + outerRadius) / outerRadius : 0;
  const outerCircleRotationCount = outerCircleRotationAngle / (2 * Math.PI);

  // 计算外圆中心位置
  const outerCircleCenter = calculateOuterCircleCenter(innerRadius, outerRadius, currentAngle);

  // 计算标记点位置（考虑方向）
  const markerPosition = calculateEpicycloidPoint(
    innerRadius,
    outerRadius,
    currentAngle,
    markerAngle * directionMultiplier
  );

  // 计算理论距离
  const theoreticalCenterDistance = calculateCenterTrajectoryLength(innerRadius, outerRadius, outerCircleRotationCount);
  const theoreticalMarkerDistance = calculateMarkerTrajectoryLength(innerRadius, outerRadius, outerCircleRotationCount);

  // 检查外圆是否回到原始位置
  const isBackToStart = () => {
    const startPosition = calculateOuterCircleCenter(innerRadius, outerRadius, 0);
    const currentPosition = outerCircleCenter;
    const distance = calculateDistance(startPosition, currentPosition);
    return distance < 1 && Math.abs(currentAngle) > Math.PI; // 至少转过半圈才检查
  };

  const animate = () => {
    if (!isRunning) return;
    
    setCurrentAngle(prev => {
      const newAngle = prev + speed * 0.02 * directionMultiplier;
      
      // 计算新的外圆转动圈数
      const newOuterRotationAngle = outerRadius > 0 ? Math.abs(newAngle) * (innerRadius + outerRadius) / outerRadius : 0;
      const newRotationCount = newOuterRotationAngle / (2 * Math.PI);
      
      // 检查是否达到目标圈数
      if (targetRotations > 0 && newRotationCount >= targetRotations) {
        setIsRunning(false);
        return newAngle;
      }
      
      // 检查是否回到原始位置（仅在没有设置目标圈数时）
      if (targetRotations === 0) {
        const newOuterCenter = calculateOuterCircleCenter(innerRadius, outerRadius, newAngle);
        const startPosition = calculateOuterCircleCenter(innerRadius, outerRadius, 0);
        const distanceToStart = calculateDistance(startPosition, newOuterCenter);
        
        if (distanceToStart < 1 && Math.abs(newAngle) > Math.PI * 2) {
          setIsRunning(false);
          return newAngle;
        }
      }
      
      return newAngle;
    });
    
    animationRef.current = requestAnimationFrame(animate);
  };

  const startAnimation = () => {
    if (!isRunning) {
      setIsRunning(true);
      // 初始化距离统计
      const initialCenter = calculateOuterCircleCenter(innerRadius, outerRadius, currentAngle);
      const initialMarker = calculateEpicycloidPoint(innerRadius, outerRadius, currentAngle, markerAngle * directionMultiplier);
      setLastCenterPosition(initialCenter);
      setLastMarkerPosition(initialMarker);
    }
  };

  const stopAnimation = () => {
    setIsRunning(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  };

  const resetAnimation = () => {
    stopAnimation();
    setCurrentAngle(0);
    setTrajectoryPoints([]);
    setShadowCircles([]);
    setActualCenterDistance(0);
    setActualMarkerDistance(0);
    setCompletedRotations(0);
    const initialCenter = calculateOuterCircleCenter(innerRadius, outerRadius, 0);
    const initialMarker = calculateEpicycloidPoint(innerRadius, outerRadius, 0, markerAngle * directionMultiplier);
    setLastCenterPosition(initialCenter);
    setLastMarkerPosition(initialMarker);
  };

  // 更新轨迹点和距离统计
  useEffect(() => {
    if (isRunning && currentAngle !== 0) {
      setTrajectoryPoints(prev => {
        const newPoints = [...prev, markerPosition];
        return newPoints;
      });

      // 更新实际距离统计
      const centerDist = calculateDistance(lastCenterPosition, outerCircleCenter);
      const markerDist = calculateDistance(lastMarkerPosition, markerPosition);
      
      setActualCenterDistance(prev => prev + centerDist);
      setActualMarkerDistance(prev => prev + markerDist);
      
      setLastCenterPosition(outerCircleCenter);
      setLastMarkerPosition(markerPosition);

      // 检查是否完成了整圈，添加灰色留影
      const currentRotations = Math.floor(outerCircleRotationCount);
      if (currentRotations > completedRotations) {
        setShadowCircles(prev => [...prev, { ...outerCircleCenter }]);
        setCompletedRotations(currentRotations);
      }
    }
  }, [markerPosition, outerCircleCenter, isRunning, currentAngle, lastCenterPosition, lastMarkerPosition, outerCircleRotationCount, completedRotations]);

  // 启动动画循环
  useEffect(() => {
    if (isRunning) {
      animationRef.current = requestAnimationFrame(animate);
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRunning, speed, innerRadius, outerRadius, clockwise]);

  // 当参数改变时重置动画
  useEffect(() => {
    resetAnimation();
  }, [innerRadius, outerRadius, markerAngle, clockwise, targetRotations]);

  return {
    currentAngle,
    trajectoryPoints,
    rotationCount: outerCircleRotationCount,
    outerCircleCenter,
    markerPosition,
    shadowCircles,
    centerDistance: theoreticalCenterDistance,
    markerDistance: theoreticalMarkerDistance,
    actualCenterDistance,
    actualMarkerDistance,
    isRunning,
    startAnimation,
    stopAnimation,
    resetAnimation,
  };
};
