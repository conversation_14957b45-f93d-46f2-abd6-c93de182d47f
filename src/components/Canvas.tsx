
import { useEffect, useRef } from "react";
import { Point } from "@/utils/mathUtils";

interface CanvasProps {
  innerRadius: number;
  outerRadius: number;
  currentAngle: number;
  trajectoryPoints: Point[];
  showTrajectory: boolean;
  outerCircleCenter: Point;
  markerPosition: Point;
  markerAngle: number;
  shadowCircles: Point[];
  clockwise: boolean;
}

const Canvas = ({
  innerRadius,
  outerRadius,
  currentAngle,
  trajectoryPoints,
  showTrajectory,
  outerCircleCenter,
  markerPosition,
  markerAngle,
  shadowCircles,
  clockwise,
}: CanvasProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置画布中心
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    ctx.save();
    ctx.translate(centerX, centerY);

    // 绘制内圆（固定圆）
    ctx.strokeStyle = "#3b82f6";
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.arc(0, 0, innerRadius, 0, 2 * Math.PI);
    ctx.stroke();

    // 绘制灰色留影圆（完成整圈后的位置）
    ctx.strokeStyle = "#6b7280";
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.5;
    shadowCircles.forEach(shadowCenter => {
      ctx.beginPath();
      ctx.arc(shadowCenter.x, shadowCenter.y, outerRadius, 0, 2 * Math.PI);
      ctx.stroke();
    });
    ctx.globalAlpha = 1;

    // 绘制外圆（滚动圆）
    ctx.strokeStyle = "#10b981";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(outerCircleCenter.x, outerCircleCenter.y, outerRadius, 0, 2 * Math.PI);
    ctx.stroke();

    // 绘制外圆中心到圆心的连线
    ctx.strokeStyle = "#6b7280";
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(outerCircleCenter.x, outerCircleCenter.y);
    ctx.stroke();
    ctx.setLineDash([]);

    // 绘制外圆半径线（显示旋转）
    const directionMultiplier = clockwise ? 1 : -1;
    const radiusEndX = outerCircleCenter.x + outerRadius * Math.cos(-currentAngle * outerRadius / innerRadius + markerAngle * Math.PI / 180 * directionMultiplier);
    const radiusEndY = outerCircleCenter.y + outerRadius * Math.sin(-currentAngle * outerRadius / innerRadius + markerAngle * Math.PI / 180 * directionMultiplier);
    
    ctx.strokeStyle = "#f59e0b";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(outerCircleCenter.x, outerCircleCenter.y);
    ctx.lineTo(radiusEndX, radiusEndY);
    ctx.stroke();

    // 绘制标记点
    ctx.fillStyle = "#ef4444";
    ctx.beginPath();
    ctx.arc(markerPosition.x, markerPosition.y, 6, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制轨迹
    if (showTrajectory && trajectoryPoints.length > 1) {
      ctx.strokeStyle = "#ec4899";
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(trajectoryPoints[0].x, trajectoryPoints[0].y);
      
      for (let i = 1; i < trajectoryPoints.length; i++) {
        ctx.lineTo(trajectoryPoints[i].x, trajectoryPoints[i].y);
      }
      ctx.stroke();

      // 绘制轨迹点
      ctx.fillStyle = "#ec4899";
      trajectoryPoints.forEach((point, index) => {
        if (index % 5 === 0) { // 每5个点绘制一个小点
          ctx.beginPath();
          ctx.arc(point.x, point.y, 2, 0, 2 * Math.PI);
          ctx.fill();
        }
      });
    }

    // 绘制外圆中心点
    ctx.fillStyle = "#10b981";
    ctx.beginPath();
    ctx.arc(outerCircleCenter.x, outerCircleCenter.y, 4, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制内圆中心点
    ctx.fillStyle = "#3b82f6";
    ctx.beginPath();
    ctx.arc(0, 0, 4, 0, 2 * Math.PI);
    ctx.fill();

    ctx.restore();
  }, [innerRadius, outerRadius, currentAngle, trajectoryPoints, showTrajectory, outerCircleCenter, markerPosition, markerAngle, shadowCircles, clockwise]);

  return (
    <div className="flex justify-center">
      <canvas
        ref={canvasRef}
        width={800}
        height={600}
        className="border border-white/20 rounded-lg bg-black/20 backdrop-blur-sm"
      />
    </div>
  );
};

export default Canvas;
