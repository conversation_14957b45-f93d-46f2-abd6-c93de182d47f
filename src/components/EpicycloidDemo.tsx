import { useState } from "react";
import Canvas from "./Canvas";
import ControlPanel from "./ControlPanel";
import { useEpicycloid } from "@/hooks/useEpicycloid";

const EpicycloidDemo = () => {
  const [innerRadius, setInnerRadius] = useState(100);
  const [outerRadius, setOuterRadius] = useState(30);
  const [showTrajectory, setShowTrajectory] = useState(true);
  const [markerAngle, setMarkerAngle] = useState(0);
  const [speed, setSpeed] = useState(1);
  const [clockwise, setClockwise] = useState(true);
  const [targetRotations, setTargetRotations] = useState(0);

  const {
    currentAngle,
    trajectoryPoints,
    rotationCount,
    outerCircleCenter,
    markerPosition,
    shadowCircles,
    centerDistance,
    markerDistance,
    isRunning,
    startAnimation,
    stopAnimation,
    resetAnimation,
  } = useEpicycloid(innerRadius, outerRadius, markerAngle, speed, clockwise, targetRotations);

  const handleStart = () => {
    startAnimation();
  };

  const handleStop = () => {
    stopAnimation();
  };

  const handleReset = () => {
    resetAnimation();
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <div className="lg:col-span-1">
        <ControlPanel
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          isAnimating={isRunning}
          showTrajectory={showTrajectory}
          markerAngle={markerAngle}
          speed={speed}
          clockwise={clockwise}
          targetRotations={targetRotations}
          rotationCount={rotationCount}
          centerDistance={centerDistance}
          markerDistance={markerDistance}
          onInnerRadiusChange={setInnerRadius}
          onOuterRadiusChange={setOuterRadius}
          onShowTrajectoryChange={setShowTrajectory}
          onMarkerAngleChange={setMarkerAngle}
          onSpeedChange={setSpeed}
          onClockwiseChange={setClockwise}
          onTargetRotationsChange={setTargetRotations}
          onStart={handleStart}
          onStop={handleStop}
          onReset={handleReset}
        />
      </div>
      <div className="lg:col-span-3">
        <Canvas
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          currentAngle={currentAngle}
          trajectoryPoints={trajectoryPoints}
          showTrajectory={showTrajectory}
          outerCircleCenter={outerCircleCenter}
          markerPosition={markerPosition}
          markerAngle={markerAngle}
          shadowCircles={shadowCircles}
          clockwise={clockwise}
        />
      </div>
    </div>
  );
};

export default EpicycloidDemo;
