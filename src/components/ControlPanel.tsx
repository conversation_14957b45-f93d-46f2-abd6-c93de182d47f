
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Pause, RotateCcw, RotateCw, Globe } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/translations";

interface ControlPanelProps {
  innerRadius: number;
  outerRadius: number;
  isAnimating: boolean;
  showTrajectory: boolean;
  markerAngle: number;
  speed: number;
  clockwise: boolean;
  targetRotations: number;
  rotationCount: number;
  centerDistance: number;
  markerDistance: number;
  onInnerRadiusChange: (value: number) => void;
  onOuterRadiusChange: (value: number) => void;
  onShowTrajectoryChange: (value: boolean) => void;
  onMarkerAngleChange: (value: number) => void;
  onSpeedChange: (value: number) => void;
  onClockwiseChange: (value: boolean) => void;
  onTargetRotationsChange: (value: number) => void;
  onStart: () => void;
  onStop: () => void;
  onReset: () => void;
}

const ControlPanel = ({
  innerRadius,
  outerRadius,
  isAnimating,
  showTrajectory,
  markerAngle,
  speed,
  clockwise,
  targetRotations,
  rotationCount,
  centerDistance,
  markerDistance,
  onInnerRadiusChange,
  onOuterRadiusChange,
  onShowTrajectoryChange,
  onMarkerAngleChange,
  onSpeedChange,
  onClockwiseChange,
  onTargetRotationsChange,
  onStart,
  onStop,
  onReset,
}: ControlPanelProps) => {
  const { language, setLanguage } = useLanguage();

  return (
    <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
      <CardHeader>
        <CardTitle className="text-center text-cyan-300">{t('controlPanel', language)}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Language Selection */}
        <div className="space-y-2">
          <Label className="text-cyan-200 flex items-center">
            <Globe className="w-4 h-4 mr-1" />
            {t('language', language)}
          </Label>
          <Select value={language} onValueChange={(value) => setLanguage(value as 'en' | 'zh')}>
            <SelectTrigger className="bg-white/10 border-white/30 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="zh">中文</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Control buttons */}
        <div className="flex gap-2">
          {!isAnimating ? (
            <Button 
              onClick={onStart} 
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <Play className="w-4 h-4 mr-2" />
              {t('start', language)}
            </Button>
          ) : (
            <Button onClick={onStop} className="flex-1 bg-red-600 hover:bg-red-700">
              <Pause className="w-4 h-4 mr-2" />
              {t('stop', language)}
            </Button>
          )}
          <Button onClick={onReset} variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20">
            <RotateCcw className="w-4 h-4" />
          </Button>
        </div>

        {/* Inner radius control */}
        <div className="space-y-2">
          <Label className="text-cyan-200">{t('innerRadius', language)}: {innerRadius}</Label>
          <Slider
            value={[innerRadius]}
            onValueChange={(value) => onInnerRadiusChange(value[0])}
            min={50}
            max={200}
            step={5}
            className="w-full"
            disabled={isAnimating}
          />
        </div>

        {/* Outer radius control */}
        <div className="space-y-2">
          <Label className="text-cyan-200">{t('outerRadius', language)}: {outerRadius}</Label>
          <Slider
            value={[outerRadius]}
            onValueChange={(value) => onOuterRadiusChange(value[0])}
            min={10}
            max={100}
            step={5}
            className="w-full"
            disabled={isAnimating}
          />
        </div>

        {/* Marker angle control */}
        <div className="space-y-2">
          <Label className="text-cyan-200">{t('markerAngle', language)}: {markerAngle}°</Label>
          <Slider
            value={[markerAngle]}
            onValueChange={(value) => onMarkerAngleChange(value[0])}
            min={0}
            max={360}
            step={15}
            className="w-full"
            disabled={isAnimating}
          />
        </div>

        {/* Target rotations control */}
        <div className="space-y-2">
          <Label className="text-cyan-200">{t('targetRotations', language)}</Label>
          <Input
            type="number"
            value={targetRotations}
            onChange={(e) => onTargetRotationsChange(Number(e.target.value))}
            min={0}
            max={100}
            className="bg-white/10 border-white/30 text-white"
            disabled={isAnimating}
            placeholder="0"
          />
        </div>

        {/* Speed control */}
        <div className="space-y-2">
          <Label className="text-cyan-200">{t('speed', language)}: {speed.toFixed(1)}x</Label>
          <Slider
            value={[speed]}
            onValueChange={(value) => onSpeedChange(value[0])}
            min={0.1}
            max={3}
            step={0.1}
            className="w-full"
            disabled={isAnimating}
          />
        </div>

        {/* Direction control */}
        <div className="flex items-center space-x-2">
          <Switch
            id="direction"
            checked={clockwise}
            onCheckedChange={onClockwiseChange}
            disabled={isAnimating}
          />
          <Label htmlFor="direction" className="text-cyan-200 flex items-center">
            {clockwise ? <RotateCw className="w-4 h-4 mr-1" /> : <RotateCcw className="w-4 h-4 mr-1" />}
            {clockwise ? t('clockwise', language) : t('counterclockwise', language)}
          </Label>
        </div>

        {/* Show trajectory switch */}
        <div className="flex items-center space-x-2">
          <Switch
            id="trajectory"
            checked={showTrajectory}
            onCheckedChange={onShowTrajectoryChange}
          />
          <Label htmlFor="trajectory" className="text-cyan-200">{t('showTrajectory', language)}</Label>
        </div>

        {/* Statistics */}
        <div className="p-4 bg-black/30 rounded-lg">
          <h3 className="text-lg font-semibold text-cyan-300 mb-2">{t('statistics', language)}</h3>
          <p className="text-cyan-100">
            {t('currentRotations', language)}: <span className="font-mono text-xl text-yellow-300">{rotationCount.toFixed(2)}</span>
          </p>
          {targetRotations > 0 && (
            <p className="text-cyan-100">
              {t('targetRotationsLabel', language)}: <span className="font-mono text-xl text-orange-300">{targetRotations}</span>
            </p>
          )}
          <p className="text-cyan-100">
            {t('centerDistance', language)}: <span className="font-mono text-xl text-green-300">{centerDistance.toFixed(1)}</span> px
          </p>
          <p className="text-cyan-100">
            {t('markerDistance', language)}: <span className="font-mono text-xl text-purple-300">{markerDistance.toFixed(1)}</span> px
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ControlPanel;
