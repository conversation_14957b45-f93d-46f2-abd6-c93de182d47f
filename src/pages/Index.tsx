
import EpicycloidDemo from "@/components/EpicycloidDemo";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/translations";

const Index = () => {
  const { language } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            {t('title', language)}
          </h1>
          <p className="text-xl text-blue-200">
            {t('subtitle', language)}
          </p>
        </div>
        <EpicycloidDemo />
      </div>
    </div>
  );
};

export default Index;
